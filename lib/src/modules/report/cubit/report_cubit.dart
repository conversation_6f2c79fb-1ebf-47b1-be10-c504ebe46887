import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/report_state.dart';
import 'package:koc_app/src/modules/report/data/model/conversion/conversion_summary_response.dart';
import 'package:koc_app/src/modules/report/data/model/performance_monthly_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class ReportCubit extends BaseCubit<ReportState> with FilterMixin {
  final ReportRepository reportRepository;
  bool _isLoadingData = false;

  ReportCubit(this.reportRepository) : super(ReportState());

  Future<void> findReportData({bool forceRefresh = false}) async {
    if (_isLoadingData && !forceRefresh) return;
    await _loadReportData();
  }

  Future<void> refreshAfterSiteSwitch() async {
    if (_isLoadingData) return;

    emit(state.copyWith(isSiteSwitching: true));
    try {
      // Reset state to clear old data before loading new data
      _clearReportData();

      // Wait a bit to ensure site switching is complete
      await Future.delayed(const Duration(milliseconds: 100));

      await _loadReportData();
    } finally {
      emit(state.copyWith(isSiteSwitching: false));
    }
  }

  /// Clear report data to prevent showing stale data during site switching
  void _clearReportData() {
    emit(state.copyWith(
      currentOneYearClickCount: {},
      currentOneYearConversionCount: {},
      performanceMonthlyData: [],
      thisMonthOccurredConversionCount: 0,
      lastMonthApprovedReward: 0,
      topTenCampaignsClickCount: [],
      paymentSummary: null,
      minimumPaymentDetails: null,
      currency: '',
      currencyCode: '',
      country: null,
      selectedSiteId: 0,
      errorMessage: '',
      isSiteSwitching: true,
    ));
  }

  Future<void> _loadReportData() async {
    if (_isLoadingData) return;
    _isLoadingData = true;

    try {
      DateTimeRange range = getTimeRange(ReportPeriod.LAST_12_MONTHS, null, null);
      int? siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      String? countryCode = await commonCubit.sharedPreferencesService.getCountryCode();

      debugPrint('🔄 ReportCubit: Loading report data for siteId: $siteId, country: $countryCode');

      if (siteId == null) {
        emit(state.copyWith(errorMessage: 'Site ID is not available. Please select a site.'));
        return;
      }

      if (countryCode == null) {
        emit(state.copyWith(errorMessage: 'Country code is not available.'));
        return;
      }

      List<dynamic> results = await Future.wait([
        reportRepository.findPerformanceMonthlyReportData(
          range.start.toZonedIso8601(countryCode.toCountry),
          range.end.toZonedIso8601(countryCode.toCountry),
          ReportQueryPeriodBase.CONVERSION_DATE,
          null,
          null,
          siteId,
        ),
        reportRepository.findConversionSummary(siteId),
        reportRepository.findTopTenCampaignsClickCount(
          range.start.toZonedIso8601(countryCode.toCountry),
          range.end.toZonedIso8601(countryCode.toCountry),
          siteId,
        ),
        reportRepository.findPaymentSummary(),
        reportRepository.findMinimumPaymentDetails(),
      ]);
      final performanceMonthlyData = results[0] as List<PerformanceMonthlyReportData>;

      Map<DateTime, int> currentOneYearClickCount = {};
      Map<DateTime, int> currentOneYearConversionCount = {};

      for (final monthlyData in performanceMonthlyData) {
        try {
          if (monthlyData.month != null) {
            final DateTime monthDate = monthlyData.month!.toDateTime(yearMonthFormat);
            final DateTime monthKey = DateTime(monthDate.year, monthDate.month, 1);

            currentOneYearClickCount[monthKey] = monthlyData.clicks;
            currentOneYearConversionCount[monthKey] = monthlyData.conversions;
          }
        } catch (e) {
          debugPrint('Error parsing month "${monthlyData.month}": $e');
          continue;
        }
      }

      ConversionSummaryResponse conversionSummaryResponse = results[1];

      emit(state.copyWith(
          currentOneYearClickCount: currentOneYearClickCount,
          currentOneYearConversionCount: currentOneYearConversionCount,
          performanceMonthlyData: performanceMonthlyData,
          thisMonthOccurredConversionCount: conversionSummaryResponse.countConversionOccurredThisMonth,
          lastMonthApprovedReward: conversionSummaryResponse.rewardApprovedLastMonth,
          topTenCampaignsClickCount: results[2].data,
          paymentSummary: results[3],
          minimumPaymentDetails: results[4],
          currency: conversionSummaryResponse.currencyCode,
          country: countryCode.toCountry,
          selectedSiteId: siteId,
          errorMessage: ''));

      debugPrint('✅ ReportCubit: Successfully loaded report data for siteId: $siteId');
    } on DioException catch (e) {
      debugPrint('❌ ReportCubit: Error loading report data: $e');
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      _isLoadingData = false;
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes report data while bypassing cache to ensure fresh data
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh() async {
    if (_isLoadingData) return;

    try {
      emit(state.copyWith(isPullToRefresh: true));

      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        emit(state.copyWith(errorMessage: 'Site ID is not available. Please select a site.'));
        return;
      }

      final apiService = Modular.get<ApiService>();
      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/monthly');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/conversion-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/campaign/chart');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/payment-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/countries/minimum-payment-details');

      await _loadReportData();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }
}
